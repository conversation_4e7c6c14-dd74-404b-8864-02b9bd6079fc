.home-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.loading-state {
  text-align: center;
  padding: 40px;
  font-size: 18px;
}

.error-state {
  text-align: center;
  padding: 40px;

  .error-message {
    color: #d32f2f;
    font-size: 16px;
    margin-bottom: 20px;
  }

  .retry-button {
    background-color: #1976d2;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;

    &:hover {
      background-color: #1565c0;
    }
  }
}

.content {
  h1 {
    color: #333;
    margin-bottom: 30px;
  }

  h2 {
    color: #555;
    margin-top: 30px;
    margin-bottom: 15px;
  }

  h3 {
    color: #666;
    margin-top: 25px;
    margin-bottom: 10px;
  }
}

.user-info, .permissions-info, .debug-info {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;

  pre {
    background-color: #fff;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #ddd;
    overflow-x: auto;
    font-size: 12px;
    line-height: 1.4;
  }

  p {
    margin: 8px 0;
  }
}
