<div class="home-container">
  <div *ngIf="loading" class="loading-state">
    <p>Loading user data and permissions...</p>
  </div>

  <div *ngIf="error" class="error-state">
    <p class="error-message">{{ error }}</p>
    <button (click)="initializeComponent()" class="retry-button">Retry</button>
  </div>

  <div *ngIf="!loading && !error" class="content">
    <h1>Welcome to Content Enrichment</h1>

    <div class="user-info" *ngIf="user">
      <h2>User Information</h2>
      <p><strong>Subscription ID:</strong> {{ subscriptionId }}</p>
      <p><strong>User Data:</strong></p>
      <pre>{{ user | json }}</pre>
    </div>

    <div class="permissions-info" *ngIf="permissions">
      <h2>Permissions</h2>
      <pre>{{ permissions | json }}</pre>
    </div>

    <div class="debug-info">
      <h3>Debug Information</h3>
      <p><strong>Subscription ID from localStorage:</strong> {{ subscriptionId }}</p>
      <p><strong>User loaded:</strong> {{ !!user }}</p>
      <p><strong>Permissions loaded:</strong> {{ !!permissions }}</p>
    </div>
  </div>
</div>
