// NOTE: This file uses $localize for i18n. Ensure you have @angular/localize installed and import '@angular/localize/init' in your main.ts.
import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from '@angular/router';
import { Observable } from 'rxjs';
// import { PermissionsService } from '../services/permissions.service';
import { AuthService } from '@auth0/auth0-angular';
import { Auth0Service } from '../../services/auth0.service';
import { UserService } from '../../services/user.service';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class AppAuthGuard implements CanActivate {
  subscriptionId: string | null | undefined;
  user: any;

  constructor(
    private router: Router,
    public auth: AuthService,
    public auth0: Auth0Service,
    // public permissionService: PermissionsService,
    public userService: UserService
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    _state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return new Promise<boolean>((resolve) => {
      this.auth.isAuthenticated$.subscribe((isAuthenticated) => {
        console.log('Auth Guard - Is authenticated:', isAuthenticated);

        if (!isAuthenticated) {
          console.log('Auth Guard - User not authenticated, denying access');
          resolve(false);
          return;
        }

        this.user = JSON.parse(localStorage.getItem('user') || 'null');
        this.subscriptionId = route.queryParams['sub'];

        console.log('Auth Guard - Route query params:', route.queryParams);
        console.log('Auth Guard - Subscription ID from URL:', this.subscriptionId);
        console.log('Auth Guard - Subscription ID from localStorage:', localStorage.getItem('SubscriptionID'));

        // If subscription ID is not in URL, try to get it from localStorage
        if (!this.subscriptionId) {
          this.subscriptionId = localStorage.getItem('SubscriptionID');
          console.log('Auth Guard - Using subscription ID from localStorage:', this.subscriptionId);
        }

        // proceed only if subs id present
        if (this.subscriptionId) {
          console.log('Auth Guard - Subscription ID found, storing and allowing access:', this.subscriptionId);
          localStorage.setItem('SubscriptionID', this.subscriptionId);
          resolve(true);
        } else {
          console.log('Auth Guard - No subscription ID found, redirecting to loading');
          this.router.navigate(['/loading'], { queryParams: route.queryParams });
          resolve(false);
        }
      });
    });
  }

  /***
   * Reject User Auth
   */
  rejectAuthentication = (errResp: string) => {
    this.auth0.logUserOut();
    alert(errResp);
  };

  /**
   * Redirect user when no module subscription found
   */
  redirectUser = (): void => {
    this.user = JSON.parse(localStorage.getItem('user') || 'null');
    if (this.user && this.user.return_to) {
      console.log(this.user.return_to);
      window.location.href = 'https://' + this.user.return_to;
    } else {
      window.location.href = 'https://' + environment.default_return_url;
    }
  };
}
