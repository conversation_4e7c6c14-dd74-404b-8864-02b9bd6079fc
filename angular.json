{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"content-enrichment": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/content-enrichment", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js", "@angular/localize/init"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "src/assets", "output": "/assets"}, {"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "node_modules/cax-design-system/resources", "output": "/assets/"}], "styles": ["src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "content-enrichment:build:production"}, "development": {"buildTarget": "content-enrichment:build:development"}}, "defaultConfiguration": "development", "options": {"proxyConfig": "proxy.conf.json"}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"outFile": "messages.en.xlf", "outputPath": "src/locale"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "node_modules/cax-design-system/resources", "output": "/assets/"}], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "eefec3b1-7e3a-4dd1-bda9-f5a67e2165a7"}}