import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { UserService } from '../../../services/user.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-home',
  imports: [CommonModule],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit {
  subscriptionId: string | null = null;
  user: any = null;
  permissions: any = null;
  loading: boolean = true;
  error: string | null = null;

  constructor(
    private activatedRoute: ActivatedRoute,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.initializeComponent();
  }

  public initializeComponent(): void {
    // Get subscription ID from URL params or localStorage
    this.activatedRoute.queryParams.subscribe(params => {
      this.subscriptionId = params['sub'] || localStorage.getItem('SubscriptionID');

      if (this.subscriptionId) {
        console.log('Home Component - Subscription ID found:', this.subscriptionId);
        localStorage.setItem('SubscriptionID', this.subscriptionId);
        this.loadUserData();
      } else {
        console.error('Home Component - No subscription ID found');
        this.error = 'No subscription ID found. Please ensure you have proper access.';
        this.loading = false;
      }
    });
  }

  private loadUserData(): void {
    if (!this.subscriptionId) {
      this.error = 'No subscription ID available';
      this.loading = false;
      return;
    }

    console.log('Home Component - Loading user data for subscription:', this.subscriptionId);

    // Load user profile and theme
    this.userService.me(this.subscriptionId).subscribe({
      next: (response) => {
        console.log('Home Component - Me API response:', response);
        this.user = response.result;
        localStorage.setItem('user', JSON.stringify(response.result));

        // Apply theme
        this.userService.setTheme(this.subscriptionId!);

        // Load permissions after user data is loaded
        this.loadPermissions();
      },
      error: (error) => {
        console.error('Home Component - Me API error:', error);
        this.error = `Failed to load user data: ${error.message || error}`;
        this.loading = false;
      }
    });
  }

  private loadPermissions(): void {
    if (!this.subscriptionId) {
      this.error = 'No subscription ID available for permissions';
      this.loading = false;
      return;
    }

    console.log('Home Component - Loading permissions for subscription:', this.subscriptionId);

    this.userService.getAppPermissions(this.subscriptionId).subscribe({
      next: (response) => {
        console.log('Home Component - Permissions API response:', response);
        this.permissions = response.result;
        this.loading = false;
      },
      error: (error) => {
        console.error('Home Component - Permissions API error:', error);
        this.error = `Failed to load permissions: ${error.message || error}`;
        this.loading = false;
      }
    });
  }
}
