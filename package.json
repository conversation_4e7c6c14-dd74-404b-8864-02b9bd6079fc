{"name": "content-enrichment", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --host m2.app-local.datax.ai --port 3000 --ssl true ", "build": "ng build", "postbuild": "bash ./build-utils/post-build.sh", "build-dev": "ng build --configuration=dev && npm run postbuild", "build-staging": "ng build --configuration=stag && npm run postbuild", "build-prod": "ng build --configuration=production && npm run postbuild", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.3", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/localize": "^19.2.14", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@auth0/auth0-angular": "^2.2.3", "cax-design-system": "^2.7.10", "caxicons": "^2.0.1", "express": "^4.18.2", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.3", "@angular/cli": "^19.2.3", "@angular/compiler-cli": "^19.2.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.2"}}